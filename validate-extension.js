// Extension validation script
// Run this in the browser console to check extension functionality

console.log('🔍 Validating Ticket Snippet Manager Extension...\n');

// Check if extension files exist and are properly structured
function validateExtensionStructure() {
    console.log('📁 Checking extension structure...');
    
    const requiredFiles = [
        'manifest.json',
        'popup.html',
        'popup.css', 
        'popup.js',
        'content.js',
        'background.js'
    ];
    
    // This would need to be run from the extension context
    console.log('✅ Required files should be present:', requiredFiles.join(', '));
}

// Check if target elements are found on the page
function validateTargetElements() {
    console.log('\n🎯 Checking for target elements...');
    
    const froalaElements = document.querySelectorAll('.fr-element.fr-view');
    const contentEditables = document.querySelectorAll('[contenteditable="true"]');
    const textInputs = document.querySelectorAll('textarea, input[type="text"]');
    
    console.log(`✅ Found ${froalaElements.length} Froala editor elements`);
    console.log(`✅ Found ${contentEditables.length} contenteditable elements`);
    console.log(`✅ Found ${textInputs.length} text input elements`);
    
    const totalTargets = froalaElements.length + contentEditables.length + textInputs.length;
    
    if (totalTargets > 0) {
        console.log(`✅ Total target elements: ${totalTargets}`);
        return true;
    } else {
        console.log('❌ No target elements found on this page');
        return false;
    }
}

// Check if Chrome extension APIs are available
function validateExtensionAPI() {
    console.log('\n🔌 Checking extension API availability...');
    
    if (typeof chrome !== 'undefined') {
        console.log('✅ Chrome API available');
        
        if (chrome.runtime) {
            console.log('✅ Chrome runtime API available');
        } else {
            console.log('❌ Chrome runtime API not available');
        }
        
        if (chrome.storage) {
            console.log('✅ Chrome storage API available');
        } else {
            console.log('❌ Chrome storage API not available');
        }
        
        return true;
    } else {
        console.log('❌ Chrome API not available (extension not loaded or not in extension context)');
        return false;
    }
}

// Test snippet insertion simulation
function simulateSnippetInsertion() {
    console.log('\n💉 Testing snippet insertion simulation...');
    
    const testText = 'Test snippet insertion';
    const targetElement = document.querySelector('.fr-element.fr-view, [contenteditable="true"], textarea, input[type="text"]');
    
    if (!targetElement) {
        console.log('❌ No target element found for testing');
        return false;
    }
    
    console.log(`✅ Found target element: ${targetElement.tagName} ${targetElement.className || '(no class)'}`);
    
    // Focus the element
    targetElement.focus();
    console.log('✅ Element focused');
    
    // Simulate text insertion
    if (targetElement.isContentEditable) {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            range.deleteContents();
            range.insertNode(document.createTextNode(testText));
            console.log('✅ Text inserted into contenteditable element');
        }
    } else if (targetElement.tagName === 'TEXTAREA' || targetElement.tagName === 'INPUT') {
        const start = targetElement.selectionStart || 0;
        const end = targetElement.selectionEnd || 0;
        const value = targetElement.value || '';
        targetElement.value = value.substring(0, start) + testText + value.substring(end);
        console.log('✅ Text inserted into input element');
    }
    
    return true;
}

// Check manifest.json structure (would need to be loaded separately)
function validateManifest() {
    console.log('\n📋 Manifest validation...');
    console.log('ℹ️  Manifest should include:');
    console.log('   - manifest_version: 3');
    console.log('   - permissions: storage, activeTab, scripting');
    console.log('   - host_permissions: <all_urls>');
    console.log('   - action with popup.html');
    console.log('   - background service worker');
    console.log('   - content_scripts for all URLs');
}

// Run all validations
function runAllValidations() {
    console.log('🚀 Starting extension validation...\n');
    
    validateExtensionStructure();
    const hasTargets = validateTargetElements();
    const hasAPI = validateExtensionAPI();
    validateManifest();
    
    if (hasTargets) {
        simulateSnippetInsertion();
    }
    
    console.log('\n📊 Validation Summary:');
    console.log(`   Target elements: ${hasTargets ? '✅' : '❌'}`);
    console.log(`   Extension API: ${hasAPI ? '✅' : '❌'}`);
    
    if (hasTargets && hasAPI) {
        console.log('\n🎉 Extension should work on this page!');
        console.log('\n📝 Next steps:');
        console.log('   1. Load the extension in Chrome/Brave developer mode');
        console.log('   2. Navigate to brave://extensions/ or chrome://extensions/');
        console.log('   3. Enable "Developer mode"');
        console.log('   4. Click "Load unpacked" and select the extension folder');
        console.log('   5. Test snippet creation and insertion');
    } else {
        console.log('\n⚠️  Some issues detected. Check the logs above.');
    }
}

// Auto-run validation
runAllValidations();

// Export functions for manual testing
window.snippetValidation = {
    validateTargetElements,
    validateExtensionAPI,
    simulateSnippetInsertion,
    runAllValidations
};
