// Background script for Ticket Snippet Manager
// Handles extension lifecycle, storage management, and communication

class SnippetManagerBackground {
    constructor() {
        this.init();
    }

    init() {
        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstallation(details);
        });

        // Handle extension startup
        chrome.runtime.onStartup.addListener(() => {
            this.handleStartup();
        });

        // Handle messages from content scripts and popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Handle storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChange(changes, namespace);
        });

        // Handle tab updates (optional: for future features)
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });
    }

    async handleInstallation(details) {
        console.log('Snippet Manager installed:', details);

        if (details.reason === 'install') {
            // First time installation
            await this.initializeDefaultData();
            this.showWelcomeNotification();
        } else if (details.reason === 'update') {
            // Extension updated
            await this.handleUpdate(details.previousVersion);
        }
    }

    handleStartup() {
        console.log('Snippet Manager started');
        // Perform any startup tasks here
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'getSnippets':
                    const snippets = await this.getSnippets();
                    sendResponse({ success: true, snippets });
                    break;

                case 'saveSnippets':
                    await this.saveSnippets(request.snippets);
                    sendResponse({ success: true });
                    break;

                case 'exportSnippets':
                    const exportData = await this.exportSnippets();
                    sendResponse({ success: true, data: exportData });
                    break;

                case 'importSnippets':
                    await this.importSnippets(request.data);
                    sendResponse({ success: true });
                    break;

                case 'getStats':
                    const stats = await this.getUsageStats();
                    sendResponse({ success: true, stats });
                    break;

                case 'logUsage':
                    await this.logSnippetUsage(request.snippetId);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Background script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    handleStorageChange(changes, namespace) {
        if (namespace === 'sync' && changes.snippets) {
            console.log('Snippets updated:', changes.snippets);
            // Notify all tabs about the change if needed
            this.notifyTabsOfSnippetChange();
        }
    }

    handleTabUpdate(tabId, changeInfo, tab) {
        // Optional: Handle tab updates for future features
        // For example, detecting ticket system pages
        if (changeInfo.status === 'complete' && tab.url) {
            this.checkIfTicketSystemPage(tab);
        }
    }

    async initializeDefaultData() {
        const defaultSnippets = [
            {
                id: 'default-1',
                name: 'Thank you for reporting',
                text: 'Thank you for reporting this issue. We are investigating and will update you shortly.',
                createdAt: new Date().toISOString(),
                usageCount: 0
            },
            {
                id: 'default-2',
                name: 'Issue resolved',
                text: 'This issue has been resolved. Please let us know if you continue to experience any problems.',
                createdAt: new Date().toISOString(),
                usageCount: 0
            },
            {
                id: 'default-3',
                name: 'Need more information',
                text: 'To better assist you, could you please provide the following additional information:\n\n1. Steps to reproduce the issue\n2. Expected behavior\n3. Actual behavior\n4. Browser and version\n\nThank you!',
                createdAt: new Date().toISOString(),
                usageCount: 0
            }
        ];

        await chrome.storage.sync.set({ 
            snippets: defaultSnippets,
            settings: {
                showNotifications: true,
                autoFocus: true,
                insertMode: 'replace' // 'replace' or 'append'
            },
            stats: {
                totalInsertions: 0,
                installDate: new Date().toISOString()
            }
        });
    }

    async handleUpdate(previousVersion) {
        console.log(`Updated from version ${previousVersion}`);
        
        // Handle version-specific migrations here
        const currentVersion = chrome.runtime.getManifest().version;
        
        // Example migration logic
        if (this.compareVersions(previousVersion, '1.0.0') < 0) {
            // Migrate from pre-1.0.0 versions
            await this.migrateToV1();
        }
    }

    async getSnippets() {
        const result = await chrome.storage.sync.get(['snippets']);
        return result.snippets || [];
    }

    async saveSnippets(snippets) {
        await chrome.storage.sync.set({ snippets });
    }

    async exportSnippets() {
        const data = await chrome.storage.sync.get(['snippets', 'settings']);
        return {
            version: chrome.runtime.getManifest().version,
            exportDate: new Date().toISOString(),
            snippets: data.snippets || [],
            settings: data.settings || {}
        };
    }

    async importSnippets(importData) {
        if (!importData.snippets) {
            throw new Error('Invalid import data');
        }

        // Validate and sanitize imported data
        const validSnippets = importData.snippets.filter(snippet => 
            snippet.name && snippet.text && snippet.id
        );

        await chrome.storage.sync.set({ snippets: validSnippets });
    }

    async getUsageStats() {
        const result = await chrome.storage.sync.get(['stats', 'snippets']);
        const stats = result.stats || { totalInsertions: 0 };
        const snippets = result.snippets || [];
        
        return {
            ...stats,
            totalSnippets: snippets.length,
            mostUsedSnippet: this.getMostUsedSnippet(snippets)
        };
    }

    async logSnippetUsage(snippetId) {
        const result = await chrome.storage.sync.get(['snippets', 'stats']);
        const snippets = result.snippets || [];
        const stats = result.stats || { totalInsertions: 0 };

        // Update snippet usage count
        const snippetIndex = snippets.findIndex(s => s.id === snippetId);
        if (snippetIndex !== -1) {
            snippets[snippetIndex].usageCount = (snippets[snippetIndex].usageCount || 0) + 1;
            snippets[snippetIndex].lastUsed = new Date().toISOString();
        }

        // Update global stats
        stats.totalInsertions = (stats.totalInsertions || 0) + 1;
        stats.lastUsed = new Date().toISOString();

        await chrome.storage.sync.set({ snippets, stats });
    }

    getMostUsedSnippet(snippets) {
        if (snippets.length === 0) return null;
        
        return snippets.reduce((most, current) => {
            const currentUsage = current.usageCount || 0;
            const mostUsage = most.usageCount || 0;
            return currentUsage > mostUsage ? current : most;
        });
    }

    async notifyTabsOfSnippetChange() {
        const tabs = await chrome.tabs.query({});
        tabs.forEach(tab => {
            chrome.tabs.sendMessage(tab.id, { 
                action: 'snippetsUpdated' 
            }).catch(() => {
                // Ignore errors for tabs without content script
            });
        });
    }

    checkIfTicketSystemPage(tab) {
        // Optional: Detect common ticket system URLs
        const ticketSystemPatterns = [
            /jira\./i,
            /zendesk\./i,
            /freshdesk\./i,
            /servicenow\./i,
            /github\.com.*\/issues/i,
            /gitlab\.com.*\/issues/i
        ];

        const isTicketSystem = ticketSystemPatterns.some(pattern => 
            pattern.test(tab.url)
        );

        if (isTicketSystem) {
            // Could show a notification or badge
            chrome.action.setBadgeText({ 
                text: '!', 
                tabId: tab.id 
            });
            chrome.action.setBadgeBackgroundColor({ 
                color: '#007bff', 
                tabId: tab.id 
            });
        }
    }

    showWelcomeNotification() {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: 'Snippet Manager Installed',
            message: 'Click the extension icon to start managing your snippets!'
        });
    }

    compareVersions(a, b) {
        const aParts = a.split('.').map(Number);
        const bParts = b.split('.').map(Number);
        
        for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
            const aPart = aParts[i] || 0;
            const bPart = bParts[i] || 0;
            
            if (aPart < bPart) return -1;
            if (aPart > bPart) return 1;
        }
        
        return 0;
    }

    async migrateToV1() {
        // Example migration logic
        console.log('Migrating to version 1.0.0');
        // Add any necessary data migrations here
    }
}

// Initialize the background script
new SnippetManagerBackground();
