* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
}

.container {
    width: 400px;
    max-height: 600px;
    overflow-y: auto;
}

header {
    background: #fff;
    padding: 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

h1 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-danger {
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    font-size: 11px;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-edit {
    background: #28a745;
    color: white;
    padding: 4px 8px;
    font-size: 11px;
    margin-right: 4px;
}

.btn-edit:hover {
    background: #1e7e34;
}

.form-container {
    background: #fff;
    padding: 16px;
    border-bottom: 1px solid #e9ecef;
}

.form-group {
    margin-bottom: 12px;
}

label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #495057;
}

input[type="text"], textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

input[type="text"]:focus, textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

textarea {
    min-height: 80px;
    resize: vertical;
    font-family: inherit;
}

.form-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.snippets-list {
    padding: 16px;
}

.snippet-item {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.snippet-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.snippet-item.inserting {
    background: #e7f3ff;
    border-color: #007bff;
}

.snippet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.snippet-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.snippet-actions {
    display: flex;
    gap: 4px;
}

.snippet-text {
    color: #6c757d;
    font-size: 12px;
    line-height: 1.4;
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-wrap;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state p {
    margin-bottom: 8px;
}

.message {
    position: fixed;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    min-width: 200px;
    text-align: center;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.hidden {
    display: none !important;
}

/* Scrollbar styling */
.container::-webkit-scrollbar {
    width: 6px;
}

.container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
