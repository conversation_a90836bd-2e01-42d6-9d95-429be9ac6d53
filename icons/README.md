# Extension Icons

This directory contains the icons for the Ticket Snippet Manager extension.

## Icon Requirements

The extension needs the following icon sizes:
- 16x16 pixels (icon16.png) - Toolbar icon
- 32x32 pixels (icon32.png) - Windows taskbar
- 48x48 pixels (icon48.png) - Extension management page
- 128x128 pixels (icon128.png) - Chrome Web Store

## Creating Icons from SVG

The `icon.svg` file contains the master icon design. To generate the PNG files:

### Option 1: Using online converter
1. Open https://cloudconvert.com/svg-to-png
2. Upload the `icon.svg` file
3. Set the width/height to the required size
4. Download and rename to the appropriate filename

### Option 2: Using Inkscape (if installed)
```bash
inkscape icon.svg --export-png=icon16.png --export-width=16 --export-height=16
inkscape icon.svg --export-png=icon32.png --export-width=32 --export-height=32
inkscape icon.svg --export-png=icon48.png --export-width=48 --export-height=48
inkscape icon.svg --export-png=icon128.png --export-width=128 --export-height=128
```

### Option 3: Using ImageMagick (if installed)
```bash
convert icon.svg -resize 16x16 icon16.png
convert icon.svg -resize 32x32 icon32.png
convert icon.svg -resize 48x48 icon48.png
convert icon.svg -resize 128x128 icon128.png
```

## Icon Design

The icon features:
- Blue gradient background circle
- White document representing snippets
- Blue text lines representing snippet content
- Green plus icon indicating the ability to add new snippets

This design clearly communicates the extension's purpose of managing text snippets.
