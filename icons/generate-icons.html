<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Generator</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .icon-container {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        
        .icon {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 50%;
            position: relative;
            display: inline-block;
            border: 2px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .icon-16 { width: 16px; height: 16px; }
        .icon-32 { width: 32px; height: 32px; }
        .icon-48 { width: 48px; height: 48px; }
        .icon-128 { width: 128px; height: 128px; }
        
        .document {
            position: absolute;
            background: rgba(255,255,255,0.9);
            border-radius: 2px;
        }
        
        .icon-16 .document {
            width: 8px;
            height: 10px;
            top: 3px;
            left: 4px;
        }
        
        .icon-32 .document {
            width: 16px;
            height: 20px;
            top: 6px;
            left: 8px;
        }
        
        .icon-48 .document {
            width: 24px;
            height: 30px;
            top: 9px;
            left: 12px;
        }
        
        .icon-128 .document {
            width: 64px;
            height: 80px;
            top: 24px;
            left: 32px;
        }
        
        .lines {
            position: absolute;
            top: 20%;
            left: 10%;
            right: 10%;
        }
        
        .line {
            background: #007bff;
            margin: 2px 0;
            border-radius: 1px;
        }
        
        .icon-16 .line { height: 1px; margin: 1px 0; }
        .icon-32 .line { height: 2px; margin: 1px 0; }
        .icon-48 .line { height: 2px; margin: 2px 0; }
        .icon-128 .line { height: 4px; margin: 3px 0; }
        
        .plus {
            position: absolute;
            background: #28a745;
            border-radius: 50%;
            border: 1px solid #fff;
        }
        
        .icon-16 .plus {
            width: 6px;
            height: 6px;
            bottom: 1px;
            right: 1px;
        }
        
        .icon-32 .plus {
            width: 12px;
            height: 12px;
            bottom: 2px;
            right: 2px;
        }
        
        .icon-48 .plus {
            width: 18px;
            height: 18px;
            bottom: 3px;
            right: 3px;
        }
        
        .icon-128 .plus {
            width: 32px;
            height: 32px;
            bottom: 8px;
            right: 8px;
        }
        
        .plus::before,
        .plus::after {
            content: '';
            position: absolute;
            background: #fff;
            border-radius: 1px;
        }
        
        .icon-16 .plus::before,
        .icon-16 .plus::after {
            width: 3px;
            height: 1px;
            top: 2px;
            left: 1px;
        }
        
        .icon-16 .plus::after {
            width: 1px;
            height: 3px;
            top: 1px;
            left: 2px;
        }
        
        .icon-32 .plus::before,
        .icon-32 .plus::after {
            width: 6px;
            height: 2px;
            top: 5px;
            left: 3px;
        }
        
        .icon-32 .plus::after {
            width: 2px;
            height: 6px;
            top: 3px;
            left: 5px;
        }
        
        .icon-48 .plus::before,
        .icon-48 .plus::after {
            width: 10px;
            height: 2px;
            top: 8px;
            left: 4px;
        }
        
        .icon-48 .plus::after {
            width: 2px;
            height: 10px;
            top: 4px;
            left: 8px;
        }
        
        .icon-128 .plus::before,
        .icon-128 .plus::after {
            width: 16px;
            height: 4px;
            top: 14px;
            left: 8px;
        }
        
        .icon-128 .plus::after {
            width: 4px;
            height: 16px;
            top: 8px;
            left: 14px;
        }
        
        h3 {
            margin: 10px 0 5px 0;
            color: #333;
        }
        
        .instructions {
            max-width: 600px;
            margin: 20px 0;
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h2>Icon Generator</h2>
        <p>To create the PNG icons for the extension:</p>
        <ol>
            <li>Right-click on each icon below</li>
            <li>Select "Save image as..." or take a screenshot</li>
            <li>Save with the corresponding filename (icon16.png, icon32.png, etc.)</li>
            <li>Make sure the saved image is exactly the specified dimensions</li>
        </ol>
    </div>
    
    <div class="icon-container">
        <div class="icon icon-16">
            <div class="document">
                <div class="lines">
                    <div class="line" style="width: 80%;"></div>
                    <div class="line" style="width: 60%;"></div>
                    <div class="line" style="width: 70%;"></div>
                </div>
            </div>
            <div class="plus"></div>
        </div>
        <h3>16x16 (icon16.png)</h3>
    </div>
    
    <div class="icon-container">
        <div class="icon icon-32">
            <div class="document">
                <div class="lines">
                    <div class="line" style="width: 80%;"></div>
                    <div class="line" style="width: 60%;"></div>
                    <div class="line" style="width: 70%;"></div>
                    <div class="line" style="width: 50%;"></div>
                </div>
            </div>
            <div class="plus"></div>
        </div>
        <h3>32x32 (icon32.png)</h3>
    </div>
    
    <div class="icon-container">
        <div class="icon icon-48">
            <div class="document">
                <div class="lines">
                    <div class="line" style="width: 80%;"></div>
                    <div class="line" style="width: 60%;"></div>
                    <div class="line" style="width: 70%;"></div>
                    <div class="line" style="width: 50%;"></div>
                    <div class="line" style="width: 65%;"></div>
                </div>
            </div>
            <div class="plus"></div>
        </div>
        <h3>48x48 (icon48.png)</h3>
    </div>
    
    <div class="icon-container">
        <div class="icon icon-128">
            <div class="document">
                <div class="lines">
                    <div class="line" style="width: 80%;"></div>
                    <div class="line" style="width: 60%;"></div>
                    <div class="line" style="width: 70%;"></div>
                    <div class="line" style="width: 50%;"></div>
                    <div class="line" style="width: 65%;"></div>
                    <div class="line" style="width: 55%;"></div>
                </div>
            </div>
            <div class="plus"></div>
        </div>
        <h3>128x128 (icon128.png)</h3>
    </div>
</body>
</html>
